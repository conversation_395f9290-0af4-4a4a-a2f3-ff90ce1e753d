import type { <PERSON>rend<PERSON>piR<PERSON>ult, TrendStep1Params, TrendWorkflowReq } from '@/api/TrendApi'
import type { PartRequired } from '@jl-org/ts-tool'
import type { AgentTask, StepNum, TaskAction } from '../types'
import type { ParsedStreamData } from './cozeStreamApi'
import { excludeKeys, filterKeys } from '@jl-org/tool'
import { DistributionEvent, eventBus, stepButtonConfig, trendApi } from '../constants'
import { addReportByTitle, createReportCard } from './addChat'
import { callCozeStreamAPI, mockCozeStreamAPI } from './cozeStreamApi'
import { addReportItem, ButtonStateManager, createAgentTask, updateAgentTask, updateAgentTaskStatus, updateMessageById } from './create'
import { handleTrendAgentError } from './error'
import { getNextStep } from './fns'
import { createBrandStrategistCards, createContentManagerCard, createCreativeDirectorCard, createResearchAnalystCard, handleStep3, handleStep6, handleStep7, handleStep8 } from './handleStepV2'
import { trendAg } from './index'

/** 执行 Brand Strategist (step2) */
async function executeBrandStrategist(store = trendAg) {
  return await handleStep6(store) // handleStep6 实际执行的是 Brand Strategist 逻辑
}

/** 执行 Creative Director (step3) */
async function executeCreativeDirector(store = trendAg) {
  return await handleStep7(store) // handleStep7 实际执行的是 Creative Director 逻辑
}

/** 执行 Content Manager (step4) */
async function executeContentManager(store = trendAg) {
  /** 直接调用 handleStep8，因为 handleStep7 不再自动调用它 */
  return await handleStep8(store)
}

/** 执行 Research Analyst 使用 Coze 流式接口 */
export async function executeResearchAnalystWithCoze(store = trendAg) {
  const { reportStore, taskStore, stateStore } = store

  /** 使用全局按钮状态管理器 */
  const buttonManager = (store as any).globalButtonManager || new ButtonStateManager(store)

  try {
    /** 更新按钮状态为思考中 */
    buttonManager.updateThinking('正在分析小红书数据...')

    /** 创建报告项 - 使用正确的 addReportItem API */
    const reportItem = addReportItem({
      type: 'markdown',
      title: '小红书营销趋势分析',
      content: '', // 初始内容为空，避免与流式数据混合
      meta: {
        step: 'step1',
        canTransformCode: true,
      },
    }, true, true, store) // 自动打开报告面板
    
    /** 标记是否已接收到第一条内容，用于清除占位文本 */
    let isFirstContent = true

    /** 环境变量控制：是否使用真实接口 */
    const envValue = import.meta.env.VITE_USE_REAL_COZE_API
    /** 临时强制使用真实接口进行测试 */
    const useRealAPI = true // envValue === 'true'
    const cozeAPI = useRealAPI
      ? callCozeStreamAPI
      : mockCozeStreamAPI

    console.warn(`[CozeAPI] 环境变量值: "${envValue}", 强制使用${useRealAPI
      ? '真实'
      : '模拟'}接口`)
    console.warn(`[CozeAPI] 所有环境变量:`, import.meta.env)

    /** 调用 Coze 流式接口 */
    await cozeAPI(
      /** 处理流式数据 */
      (data: ParsedStreamData) => {
        console.log('[CozeAPI] ✅ 接收到流式数据:', {
          type: data.type,
          contentLength: data.content?.length || 0,
          title: data.title,
        })

        if (data.type === 'title' && data.title) {
          /** 更新报告标题 */
          const targetItem = reportStore.items.find(item => item.id === reportItem.id)
          if (targetItem) {
            targetItem.title = data.title
            console.log('[CozeAPI] ✅ 标题已更新:', data.title)
          }
        }
        else if (data.type === 'text' && data.content) {
          /** 追加文本内容 */
          const targetItem = reportStore.items.find(item => item.id === reportItem.id)
          if (targetItem) {
            /** 如果是第一条内容，直接设置；否则追加 */
            if (isFirstContent) {
              targetItem.content = data.content
              isFirstContent = false
            } else {
              targetItem.content = targetItem.content + data.content
            }
            console.log('[CozeAPI] ✅ 内容已更新，当前长度:', targetItem.content.length)
          }
        }
        else if (data.type === 'complete') {
          console.log('[CozeAPI] ✅ 数据流完成')
        }

        /** 更新按钮思考状态 */
        buttonManager.updateThinking('正在接收分析结果...')
      },
      /** 错误处理 */
      (error: Error) => {
        console.error('[CozeAPI] 接口调用失败:', error)
        buttonManager.updateThinking('分析过程中出现错误，请重试')

        /** 更新报告内容显示错误信息 */
        const updatedItems = reportStore.items.map(item =>
          item.id === reportItem.id
            ? { ...item, content: `分析过程中出现错误：${error.message}\n\n请检查网络连接或API配置后重试。` }
            : item,
        )
        reportStore.items = updatedItems

        handleTrendAgentError(true, store)
      },
      /** 完成处理 */
      () => {
        console.log('[CozeAPI] 流式响应完成')

        /** 更新按钮状态为完成 */
        buttonManager.updateThinking('')
        buttonManager.setButtonsDisabled(false)

        console.log('[CozeAPI] Research Analyst 执行完成')
      },
    )
  }
  catch (error) {
    console.error('[CozeAPI] Research Analyst 执行失败:', error)
    buttonManager.updateThinking('分析失败，请重试')
    buttonManager.setButtonsDisabled(false)
    handleTrendAgentError(true, store)
  }
}

// eslint-disable-next-line import/no-mutable-exports
export let cacheExecuteAndStream = async (
  data: PartRequired<TrendWorkflowReq, 'workflowStep'>,
  onMsg?: (data: TrendApiResult) => void,
): Promise<TrendApiResult> => {
  return {} as any
}

export async function handleFormSubmit(params: TrendStep1Params, store = trendAg): Promise<void> {
  const { messageStore, stateStore, stepState, mdToCodePreview, reportStore, taskStore } = store

  let isFirst = true
  const step = getNextStep(store).curStep

  /** 使用全局按钮状态管理器 */
  const buttonManager = (store as any).globalButtonManager || new ButtonStateManager(store)

  /** 注册第一个按钮（ChatWorkflow.tsx 中的 "Continue to Strategy" 按钮）到全局管理器 */
  const firstButtonId = 'step1-Continue to Strategy'
  if (!buttonManager.buttons.has(firstButtonId)) {
    /** 创建一个虚拟按钮消息来代表 ChatWorkflow 中的按钮 */
    const virtualButtonMessage = {
      id: `virtual-${firstButtonId}`,
      inlineAction: {
        loading: true, // 开始时设为 loading 状态
        disabled: true,
        label: 'Continue to Strategy',
        type: 'primary' as const,
        onClick: () => {},
      },
    }
    buttonManager.buttons.set(firstButtonId, virtualButtonMessage as any)
  }

  buttonManager.startThinking('Analyzing industry trends and competitor strategies...', step)

  createAgentTask(undefined, step, store)
  updateAgentTaskStatus(step, 'in-progress', store)

  /** 立即创建Research Analyst卡片并设置映射 */
  createResearchAnalystCard(step, store)

  /** 触发滚动到底部事件 */
  setTimeout(() => {
    eventBus.emit(DistributionEvent.ScrollToBottom)
  }, 100)

  trendApi.executionId = ''
  trendApi.processInstanceId = ''

  stateStore.cacheFormData = excludeKeys(params, ['pic'])

  cacheExecuteAndStream = async (data, onMsg) => {
    stateStore.isLoading = true
    const msg = await trendApi.executeAndStream(
      {
        ...data,
        params: data.workflowStep === 'step1'
          ? {
              ...params,
              ...data.params,
            }
          : {
              ...stateStore.cacheFormData,
              ...data.params,
            },
      },
      (messages) => {
        onMsg?.(messages)
      },
    )
      .finally(() => {
        stateStore.isLoading = false
      })

    if (msg.hasError) {
      stateStore.errorMsg = msg.errorMsg
      stateStore.hasError = true
    }
    return msg
  }

  stateStore.isProcessing = true
  stepState.step1.isProcessing = true

  /** 调用 Coze 流式接口执行 Research Analyst */
  try {
    await executeResearchAnalystWithCoze(store)

    /** 执行成功，更新状态 */
    updateAgentTaskStatus(step, 'complete', store)
    stepState.step1.isProcessing = false

    /** 结束思考状态，传递当前完成的步骤 */
    buttonManager.endThinking(undefined, step)

    /** 手动触发第一个按钮状态同步 */
    setTimeout(() => {
      buttonManager.syncChatWorkflowButtonState()
    }, 100)
  }
  catch (error) {
    console.error('[handleFormSubmit] Coze接口调用失败:', error)
    handleTrendAgentError(true, store)
    stepState.step1.isProcessing = false
    return
  }

  /**
   * 只为非内部报告项创建卡片
   * 内部报告项（标题以_internal_开头）不会被createReportCard处理
   */
  createReportCard(reportStore.items.filter(item =>
    item.meta.step === step && !item.title?.startsWith('_internal_'),
  ), step, store)
  // taskStore.currentStep 已经在 createAgentTask 中设置，这里不需要重复设置

  /** Research Analyst 完成，结束thinking状态并同步按钮状态 */
  buttonManager.endThinking(undefined, step)

  // Research Analyst 完成，在消息流中显示 Approve Strategy 按钮
  console.warn('[handleFormSubmit] Research Analyst 完成，创建 Approve Strategy 按钮')
  const nextButtonLabel = stepButtonConfig[step]?.nextButtonLabel
  console.warn('[handleFormSubmit] nextButtonLabel:', nextButtonLabel)

  if (nextButtonLabel) {
    /** 使用全局按钮状态管理器 */
    const globalButtonManager = (store as any).globalButtonManager || new ButtonStateManager(store)

    globalButtonManager.createManagedButton(
      {
        label: nextButtonLabel,
        type: 'primary',
        onClick: async () => {
          console.warn('[InlineButton] 按钮被点击，执行 Brand Strategist')
          /** 执行下一步：Brand Strategist */
          await executeBrandStrategist(store)
        },
      },
      {
        meta: {
          step, // 使用当前步骤，确保按钮显示在正确位置
        },
      },
    )
    console.warn('[handleFormSubmit] Approve Strategy 按钮已创建')
  }
}

export async function handleTaskAction(action: TaskAction, task: AgentTask, store = trendAg) {
  const { stateStore, stepState, taskStore } = store

  if (stateStore.hasError) {
    return
  }

  const step = task.step

  /** 处理初始的开始按钮 */
  if (action.label?.startsWith('开始执行 Research Analyst')) {
    /** 清空初始任务 */
    taskStore.agentTasks = []
    /** 执行第一个机器人 */
    const formData = stateStore.formData
    if (formData) {
      await handleFormSubmit(formData, store)
      stateStore.formData = null
    }
    return
  }

  /** 处理 Continue 按钮点击 - 根据当前步骤执行对应的智能体 */
  if (action.label?.startsWith('Continue to')) {
    switch (step) {
      case 'step0': // 初始状态，点击后执行 Research Analyst
        /** 清空初始任务 */
        taskStore.agentTasks = []
        /** 执行第一个机器人 */
        const formData = stateStore.formData
        if (formData) {
          await handleFormSubmit(formData, store)
          stateStore.formData = null
        }
        break

      case 'step1': // Research Analyst 完成，点击后执行 Brand Strategist
        stateStore.isReportOpen = true // 立即打开报告面板
        stepState.step2.isProcessing = true
        await executeBrandStrategist(store)
        stepState.step2.isProcessing = false
        break

      case 'step2': // Brand Strategist 完成，点击后执行 Creative Director
        stateStore.isReportOpen = true // 立即打开报告面板
        stepState.step3.isProcessing = true
        await executeCreativeDirector(store)
        stepState.step3.isProcessing = false
        break

      case 'step3': // Creative Director 完成，点击后执行 Content Manager
        stateStore.isReportOpen = true // 立即打开报告面板
        stepState.step4.isProcessing = true
        await executeContentManager(store)
        stepState.step4.isProcessing = false
        break

      default:
        break
    }
    return
  }

  /** 如果是"Approve"按钮，完成当前步骤并显示下一步按钮 */
  if (action.label === 'Approve') {
    const nextButtonLabel = stepButtonConfig[step]?.nextButtonLabel

    if (nextButtonLabel) {
      /** 显示下一步按钮 */
      updateAgentTask(step, {
        actions: [{ label: nextButtonLabel, type: 'primary' }],
      }, store)
    }
    else {
      /** 最后一步，流程结束 */
      updateAgentTask(step, {
        actions: [],
      }, store)
      stateStore.isProcessing = false
    }
    return
  }

  /** 兼容旧的执行按钮逻辑 */
  if (action.label?.startsWith('执行')) {
    switch (step) {
      case 'step1':
        stateStore.isReportOpen = false
        stepState.step2.isProcessing = true
        await handleStep3(store)
        stepState.step2.isProcessing = false
        break

      case 'step2':
        stateStore.isReportOpen = false
        stepState.step3.isProcessing = true
        await handleStep6(store)
        stepState.step3.isProcessing = false
        break

      case 'step3':
        stateStore.isReportOpen = false
        stepState.step4.isProcessing = true
        await handleStep7(store)
        stepState.step4.isProcessing = false
        break

      case 'step4':
        stateStore.isReportOpen = false
        await handleStep8(store)
        stateStore.isProcessing = false
        break

      default:
        break
    }
  }
}

export function handleTaskClick(task: AgentTask, taskStore = trendAg.taskStore) {
  taskStore.currentStep = task.step
  eventBus.emit(DistributionEvent.ScrollToBottom)
}
