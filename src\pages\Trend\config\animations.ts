/**
 * Trend 模块动画配置
 *
 * 统一管理所有动画效果的配置，提高一致性和可维护性
 */

import type { Variants } from 'framer-motion'

/** 开发环境动画加速配置 */
const DEV_SPEED_MULTIPLIER = import.meta.env.DEV
  ? 0.1
  : 1

/** 基础动画持续时间 */
export const ANIMATION_DURATION = {
  /** 快速动画 */
  fast: 0.15 * DEV_SPEED_MULTIPLIER,
  /** 正常动画 */
  normal: 0.3 * DEV_SPEED_MULTIPLIER,
  /** 慢速动画 */
  slow: 0.5 * DEV_SPEED_MULTIPLIER,
} as const

/** 常用的动画变体 */
export const ANIMATION_VARIANTS = {
  /** 淡入淡出 */
  fadeInOut: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  } as Variants,

  /** 从下方滑入 */
  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
  } as Variants,

  /** 从上方滑入 */
  slideDown: {
    initial: { opacity: 0, y: -20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 20 },
  } as Variants,

  /** 缩放动画 */
  scale: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 },
  } as Variants,

  /** 列表项动画 */
  listItem: {
    initial: { opacity: 0, y: -10 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -10 },
  } as Variants,
} as const

/** 动画过渡配置 */
export const ANIMATION_TRANSITIONS = {
  /** 默认过渡 */
  default: {
    duration: ANIMATION_DURATION.normal,
    ease: 'easeOut',
  },

  /** 弹性过渡 */
  spring: {
    type: 'spring',
    stiffness: 300,
    damping: 30,
  },

  /** 快速过渡 */
  fast: {
    duration: ANIMATION_DURATION.fast,
    ease: 'easeOut',
  },

  /** 慢速过渡 */
  slow: {
    duration: ANIMATION_DURATION.slow,
    ease: 'easeInOut',
  },
} as const

/** 预设的完整动画配置 */
export const PRESET_ANIMATIONS = {
  /** 报告内容动画 */
  reportContent: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: ANIMATION_TRANSITIONS.default,
  },

  /** 消息项动画 */
  messageItem: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    transition: ANIMATION_TRANSITIONS.fast,
  },

  /** 任务项动画 */
  taskItem: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    transition: ANIMATION_TRANSITIONS.spring,
  },

  /** 列表项动画（用于笔记和达人列表） */
  listItem: {
    layout: true,
    initial: { opacity: 0, y: -10 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -10 },
    transition: { duration: ANIMATION_DURATION.fast },
  },
} as const

/** 获取列表项动画配置（支持开发环境加速） */
export function getListItemAnimation() {
  return {
    ...PRESET_ANIMATIONS.listItem,
    transition: {
      duration: import.meta.env.DEV
        ? 0.05
        : 0.2,
    },
  }
}
