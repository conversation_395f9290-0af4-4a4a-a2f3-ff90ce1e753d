import type { <PERSON><PERSON><PERSON><PERSON>, PagerList } from '@/dts'
import type { ContentChunk } from '@mistralai/mistralai/models/components'
import type { Role } from './ChatGPTApi'
import type { GenVideoParams } from './GenVideoApi'
import { IS_PROD } from '@/config'
import { request } from '@/utils'
import { arrToChunk, getImg, timer } from '@jl-org/tool'
import { Mistral } from '@mistralai/mistralai'

const MODEL_POLLING_GAP = 12000
const OHTER_POLLING_GAP = 6000

export class ChatApi {
  private static client = new Mistral({ apiKey: import.meta.env.VITE_LE_CHAT_KEY })

  /**
   * 创建ai会话
   */
  static async createSession(): Promise<{ chatId: string }> {
    // return { chatId: '' }
    return request.post('/app/ai-conversation/create-session')
  }

  /**
   * 记录ai问答
   */
  static async recordSession(params: RecordSessionParams) {
    for (let i = 0; i < params.aiChatDataList.length; i++) {
      const item = params.aiChatDataList[i]
      if (!item.grokReason?.length)
        continue

      const grokReasons = grokReasonToArr(item.grokReason)
      // @ts-ignore
      item.grokReason = grokReasons
    }

    function grokReasonToArr(grokReasons: GrokReason[]) {
      const arr: string[] = []
      grokReasons.forEach((item) => {
        arr.push(item.title)
        arr.push(item.markdown)
      })

      return arr
    }

    return request.post('/app/ai-conversation/save-ai-conversation', params)
  }

  /**
   * 提交任务，预扣除用户积分
   */
  static async submitTask(chatId: string, types: SubmitEnum[]) {
    return request.post('/app/chat-task/submit', { types, chatId })
  }

  /**
   * 提交任务，扣除用户积分
   */
  static async confirmTask(chatId: string, aiChatTaskConditions: AiChatTaskConditions[]) {
    return request.post('/app/chat-task/condition', { aiChatTaskConditions, chatId })
  }

  /**
   * 分页查询用户的会话记录。(只会返回单次会话的第一条内容)
   */
  static async querySession(params: PageQuery<{ createTime?: string }>): Promise<PagerList<ChatHistoryItem>> {
    return request.get('/app/ai-conversation/page', { params })
  }

  /**
   * 分页-根据会话id查询会话详细内容
   */
  static async querySessionByChatId(params: PageQuery<{ chatId: string }>): Promise<PagerList<ChatHistoryItem>> {
    const data = await request.get('/app/ai-conversation/get-detail', { params }) as any

    function normalizeGrokReason(grokReason: string[]) {
      const arr = arrToChunk(grokReason, 2)
      const formatGrokReason: GrokReason[] = []

      for (let i = 0; i < arr.length; i++) {
        const [title, markdown] = arr[i] as [string, string]
        formatGrokReason[i] = { title, markdown }
      }
      return formatGrokReason
    }

    for (let i = 0; i < data.list.length; i++) {
      const item = data.list[i] as ChatHistoryItem
      if (!item.grokReason?.length)
        continue

      const grokReason = normalizeGrokReason(item.grokReason as unknown as string[])
      item.grokReason = grokReason
    }

    return data
  }

  /**
   * 一键生成图片并替换背景
   */
  static async oneClickImgGenImg(params: {
    frameScale: Scale
    imageUrl: string
    prompt: string
    num: number
  }) {
    const { taskId } = await request.post('/ai/generate/bg-image-generation', params) as any
    return ChatApi.checkTask<{
      imgUrls: string[]
      status: Status
    }>(taskId, 'normal')
  }

  /**
   * 图片生模型
   */
  static async imgGenModel(params: {
    imgUrl: string
  }): Promise<{ taskId: string }> {
    return request.post('/ai/generate/generate-model', params)
  }

  /**
   * 模型轮询
   */
  static async checkModel(params: {
    taskId: string
  }): Promise<{
      modelUrl: string
      renderedImageUrl: string
      status: Status
    }> {
    return request.get('/ai/generate/model-check-task', { params })
  }

  static async pollingModel(params: {
    imgUrl: string
  }) {
    let stopFn = () => { }
    const { taskId } = await ChatApi.imgGenModel(params)
    const { promise, resolve, reject } = Promise.withResolvers<{
      modelUrl: string
      renderedImageUrl: string
      status: Status
    }>()

    const polling = async () => {
      await ChatApi.checkModel({
        taskId,
      })
        .then((res) => {
          if (res.status === Status.Finished) {
            resolve(res)
            stopFn()
          }
          else if (res.status === Status.Failed) {
            reject()
            stopFn()
          }
        })
        .catch(() => {
          reject()
          stopFn()
        })
    }

    stopFn = timer(polling, MODEL_POLLING_GAP)
    return promise
  }

  /**
   * AI视频生成（二合一)
   */
  static async genVideo(
    params: GenVideoParams,
  ): Promise<{ taskId: string }> {
    const url = IS_PROD
      ? '/ai/generate/video-generation'
      : '/ai/generate/new/video-generation'
    return request.post('/ai/generate/new/video-generation', params)
  }

  /**
   * Video-轮询
   */
  static async checkVideo(params: {
    taskId: string
    type: GenVideoEnum
  }): Promise<{
      urls: string[]
      status: Status
    }> {
    const url = IS_PROD
      ? '/ai/generate/video-check-task'
      : '/ai/generate/new/video-check-task'
    return request.get('/ai/generate/new/video-check-task', { params })
  }

  static async pollingVideo(params: GenVideoParams) {
    let stopFn = () => { }
    const { taskId } = await ChatApi.genVideo(params)
    const { promise, resolve, reject } = Promise.withResolvers<string[]>()

    const polling = async () => {
      await ChatApi.checkVideo({
        taskId,
        type: params.resourceVideoGenerationTypeEnum === 'IMAGE_TO_VIDEO'
          ? GenVideoEnum.ImageToVideo
          : GenVideoEnum.TextToVideo,
      })
        .then((res) => {
          if (res.status === Status.Finished) {
            resolve(res.urls)
            stopFn()
          }
          else if (res.status === Status.Failed) {
            reject()
            stopFn()
          }
        })
        .catch(() => {
          reject()
          stopFn()
        })
    }

    stopFn = timer(polling, MODEL_POLLING_GAP)
    return promise
  }

  /**
   * 商品识别
   */
  static async imgRecognition(imageUrl: string) {
    const ids = await request.post('/ai/generate/image-recognition', { imageUrl }) as any
    return ChatApi.checkTask<{
      text: string
      status: Status
    }>(ids.taskId, 'img-recognition')
  }

  /**
   * 文生图
   */
  static async txtToImg(params: TxtToImgParams) {
    const ids = await request.post('/ai/generate/bg-image-generation', params) as any
    return ChatApi.checkTask(ids.taskId, 'txt-to-img')
  }

  /**
   * lechat
   */
  static async leChat(
    { content, onMsg, imgUrl, history = [] }: LechatParams,
  ) {
    let txt = ''
    let model: Model = 'mistral-large-latest'

    const data: ContentChunk[] = [{ type: 'text', text: content }]
    if (imgUrl) {
      model = 'pixtral-12b'
      data.push({ type: 'image_url', imageUrl: imgUrl })
    }

    const chatStream = await ChatApi.client.chat.stream({
      model,
      messages: [
        ...history,
        { role: 'user', content: data },
      ],
      stream: true,
    })

    for await (const chunk of chatStream) {
      if (chunk.data.choices.length > 0) {
        const content = chunk.data.choices[0].delta.content
        if (content) {
          txt += content
          onMsg?.(txt)
        }
      }
    }

    return txt
  }

  /**
   * 可灵生图
   */
  static async keLingGenImg(params: TxtToImgParams) {
    const ids = await request.post('/ai/generate/ke-ling-bg-image-generation', params) as any
    return ChatApi.checkTask(ids.taskId, 'keling')
  }

  /**
   * 高清放大图片
   */
  static async superResolution(imageUrl: string) {
    const ids = await request.post('/ai/generate/img-enlargement', { imageUrl }) as any
    return ChatApi.checkTask(ids.taskId)
  }

  /**
   * 抠图服务
   */
  static async cutImage(imageUrl: string) {
    const ids = await request.post('/ai/generate/remove-background', { imageUrl }) as any
    return ChatApi.checkTask(ids.taskId)
  }

  /**
   * 产品替换生图
   */
  static async productReplace(data: {
    itemImgUrl: string
    bgImageUrl: string
    sessionId?: number
  }) {
    const ids = await request.post('/ai/generate/item-replacement', data) as any
    return ChatApi.checkTask(ids.taskId, 'normal')
  }

  /**
   * 检查任务进度
   */
  static async checkTask<T = { imgUrls: string[] }>(
    taskId: string,
    type: 'normal' | 'keling' | 'txt-to-img' | 'img-recognition' = 'normal',
  ): Promise<T> {
    let timer: number
    const { promise, reject, resolve } = Promise.withResolvers<T>()

    let url = '/ai/generate/check-task'
    if (type === 'keling') {
      url = '/ai/generate/ke-ling-check-task'
    }
    else if (type === 'normal') {
      url = '/ai/generate/check-task'
    }
    else if (type === 'txt-to-img') {
      url = '/ai/generate/check-task'
    }
    else if (type === 'img-recognition') {
      url = '/ai/generate/check-text-task'
    }

    const polling = async () => {
      const data = await request.get(url, { params: { taskId } }) as any
      const status = data.status as Status
      if (status === Status.Finished) {
        clearInterval(timer)
        resolve(data)
      }
      else if (status === Status.Failed) {
        clearInterval(timer)
        reject()
      }
    }

    try {
      timer = window.setInterval(polling, OHTER_POLLING_GAP)
    }
    catch (error) {
      reject(error)
    }

    return promise
  }
}

export async function processImg(url: string) {
  const img = await getImg(url)
  if (!img)
    throw new Error('img load fail')

  let imgUrl = url
  if (img.naturalWidth < 1024 || img.naturalHeight < 1024) {
    const res = await ChatApi.superResolution(url)
    imgUrl = res.imgUrls[0]
  }

  const cutedUrls = await ChatApi.cutImage(imgUrl)
  return cutedUrls.imgUrls[0]
}

export async function batchKeling(scenes: string[], imageUrl: string) {
  const prom = scenes.map(item => ChatApi.keLingGenImg({
    frameScale: '1:1',
    prompt: `The image resolution must be 1024 * 1024. There is only one subject. ${item}`,
    imageUrl,
    num: 1,
  }))
  const result = await Promise.all(prom)
  const arr: string[] = []
  result.forEach((item) => {
    arr.push(...item.imgUrls)
  })

  return arr
}

export async function batchReplaceImg(bgUrls: string[], itemImgUrl: string) {
  const prom = bgUrls.map(bgUrl => ChatApi.productReplace({
    bgImageUrl: bgUrl,
    itemImgUrl,
  }))
  const result = await Promise.all(prom)
  const arr: string[] = []
  result.forEach((item) => {
    arr.push(...item.imgUrls)
  })

  return arr
}

export type Model = 'mistral-large-latest' | 'pixtral-12b'
export type Scale = '16:9' | '9:16' | '1:1' | '4:3' | '3:4' | '3:2' | '2:3'
/**
 * 状态：-1-生成失败，0-生成中，1-生成完成
 */
export enum Status {
  Failed = -1,
  Pending = 0,
  Finished = 1,
}

type TxtToImgParams = {
  frameScale: Scale
  imageUrl?: string
  prompt: string
  num?: number
}

export type BaseChatParams = {
  content: string
  imgUrl?: string
}

export type LechatParams = BaseChatParams & {
  onMsg?: (msg: string) => void
  history?: {
    role: Role
    content: string
  }[]
}

/**
 * 1:文生视频，2:图生视频
 */
export enum GenVideoEnum {
  TextToVideo = 1,
  ImageToVideo = 2,
}

export type RecordSessionParams = {
  chatId: string
  aiChatDataList: AiChatDataList[]
}

export type GrokReason = {
  markdown: string
  title: string
}

export type AiChatDataList = {
  /**
   * 文字内容
   */
  content: string
  role: RecordRole
  /**
   * 思考内容
   */
  reasoningContent?: string
  grokReason?: GrokReason[]

  originalImageUrls?: string[]
  composeImageUrls?: string[]
  videoUrls?: string[]
  modelUrls?: string[]
  type: SessionType
  isShow: IsShow
  layout?: string
}

export type ChatHistoryItem = {
  id: string
  chatId: string
  createUserString: string
  createTime: string
  disabled: boolean
  content: string
  role: RecordRole
  reasoningContent: string
  grokReason?: GrokReason[]
  originalImageUrls: string[]
  composeImageUrls: string[]
  layout: string
  videoUrls: string[]
  modelUrls: string[]
  type: SessionType
  isShow: IsShow
}

export enum AiChatTaskStatus {
  Failed = -1,
  Pending = 0,
  Finished = 1,
}

export type AiChatTaskConditions = {
  status: AiChatTaskStatus
  type: SubmitEnum
}

/**
 * role
 * 角色：1:user , 2:ai
 */
export enum RecordRole {
  User = 1,
  Ai = 2,
}

/**
 * 1:chat对话文字（真实），2:版权，3:报告，4:图片，5:视频，6:3D，7:其他对话文字
 */
export enum SessionType {
  Chat = 1,
  Copyright = 2,
  Report = 3,
  Image = 4,
  Video = 5,
  Model = 6,
  Other = 7,
}

/**
 * 是否展示:1:展示在页面，2:不展示在页面
 */
export enum IsShow {
  Show = 1,
  Hide = 2,
}

/**
 * 任务类型 2:版权，3:报告，4:图片，5:视频，6:3D
 */
export enum SubmitEnum {
  Copyright = 2,
  Report = 3,
  Image = 4,
  Video = 5,
  Model = 6,
  ChangeImage = 7,
}
