import { Button, Input, Space } from "antd";
import { useCallback, useState } from "react";

export const HACK_KEY = "hack_id";

export const HACK_VALUE = "^#geCsdr*hgbN";

export default function Hack() {
  const [hackId, setHackId] = useState<string>("");

  const onHack = useCallback(() => {
    if (!hackId || HACK_VALUE !== hackId) {
      return;
    }

    sessionStorage.setItem(HACK_KEY, hackId);

    window.location.reload();
  }, [hackId]);

  return (
    <Space.Compact style={{ width: "100%" }}>
      <Input value={hackId} onChange={(e) => setHackId(e.target.value)} />
      <Button type="primary" onClick={onHack}>
        Go
      </Button>
    </Space.Compact>
  );
}
