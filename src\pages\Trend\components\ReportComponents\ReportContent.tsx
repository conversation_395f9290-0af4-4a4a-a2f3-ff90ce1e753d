import type { CodePreviewMode } from '@/components/CodePreview'
import type { MdEditorRef } from '@/components/MdEditor'
import type { MdToCodePreviewType, ReportStoreType, StateStoreType } from '../../stores'
import type { BiJiData, ReportCodeMap, ReportContentItem } from '../../types'
import { CodePreview } from '@/components/CodePreview'
import { Icon } from '@/components/Icon'
import { KeepAlive } from '@/components/KeepAlive'
import { Loading } from '@/components/Loading'
import { MdEditor } from '@/components/MdEditor'
import { Switch } from '@/components/Switch'
import { god } from '@/god'
import { useAsyncEffect, useUpdateEffect } from '@/hooks'

import { cn } from '@/utils'
import { copyToClipboard } from '@jl-org/tool'
import { motion } from 'framer-motion'
import { BarChart3, Code2, Download, FileText } from 'lucide-react'
import { memo, useEffect, useRef, useState } from 'react'
import { findReportByTitle } from '../../stores/create'
import { marketReportToCode, reportToCode } from '../../stores/fns'
import { formatDuration } from '../../tool'
import { BijiDataPreview } from './BijiDataPreview'
import { BijiListPreview } from './BijiListPreview'
import { DaRenListPreview } from './DaRenListPreview'
import { PhonePreview } from './PhonePreview'

export const ReportContent = memo<ReportContentProps>((
  {
    className,
    style,
    item,
    mdToCodePreview,
    reportStore,
    stateStore,
  },
) => {
  /** 订阅 reportStore 的变化，确保实时更新 */
  const reportSnap = reportStore.use()

  /** 从 store 中获取最新的 item 数据 */
  const currentItem = reportSnap.items.find(storeItem => storeItem.id === item.id) || item
  const { title, id, type, content, meta: metadata } = currentItem

  const [isFullscreen, setIsFullscreen] = useState(false)
  const codeSnap = mdToCodePreview.use()
  const curReportCodeMap = (codeSnap as any)[title || ''] as ReportCodeMap

  /** 格式化文件大小 */
  const formatFileSize = (bytes?: number) => {
    if (!bytes)
      return ''
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / 1024 ** i).toFixed(1)} ${sizes[i]}`
  }

  const mdEditorRef = useRef<MdEditorRef>(null)
  const mdHeaderHeight = 48
  const [codeMode, setCodeMode] = useState<CodePreviewMode>('code')
  const [activeTab, setActiveTab] = useState<'markdown' | 'previewCode'>('markdown')

  useEffect(
    () => {
      const nextVal = curReportCodeMap?.done
        ? 'preview'
        : 'code'

      setCodeMode(nextVal)
    },
    [curReportCodeMap?.done],
  )

  useAsyncEffect(
    async () => {
      if (
        activeTab === 'markdown'
        || !curReportCodeMap
        || curReportCodeMap.isToCoding
        || curReportCodeMap.done
      ) {
        return
      }

      type K = keyof typeof mdToCodePreview
      const keySet = new Set<K>()

      if (['brand_report', 'industry_report'].includes(title || '')) {
        const modifyParams = {
          brand_report: findReportByTitle('brand_report')?.content || '',
          industry_report: findReportByTitle('industry_report')?.content || '',
        }
        if (!modifyParams.brand_report || !modifyParams.industry_report) {
          god.messageError('Please complete the report first')
          return
        }

        await reportToCode(modifyParams, (data) => {
          Object.entries(data).forEach(([k, v]) => {
            const key = k as K
            if (!mdToCodePreview[key]) {
              return
            }

            keySet.add(key);
            (mdToCodePreview[key] as ReportCodeMap).code = v;
            (mdToCodePreview[key] as ReportCodeMap).isToCoding = true
          })
        })

        setCodeMode('preview')
      }
      else if (['brand_market_report'].includes(title || '')) {
        await marketReportToCode(stateStore.cacheAiData.brand_market_report, (code) => {
          const key = 'brand_market_report' as K
          if (!mdToCodePreview[key]) {
            return
          }

          keySet.add(key);
          (mdToCodePreview[key] as ReportCodeMap).code = code;
          (mdToCodePreview[key] as ReportCodeMap).isToCoding = true
        })

        setCodeMode('preview')
      }

      keySet.forEach((key) => {
        (mdToCodePreview[key] as ReportCodeMap).isToCoding = false;
        (mdToCodePreview[key] as ReportCodeMap).done = true
      })
    },
    [activeTab, curReportCodeMap],
    {
      onlyRunInUpdate: true,
    },
  )

  return (
    <motion.div
      className={ cn(
        'ReportContentContainer h-full px-4 relative',
        className,
      ) }
      style={ style }
      initial={ { opacity: 0, y: 20 } }
      animate={ { opacity: 1, y: 0 } }
      transition={ { duration: 0.3 } }
      key={ codeMode }
    >
      <KeepAlive active={ type === 'text' }>
        <div className="prose prose-sm max-w-none">
          <p className="whitespace-pre-wrap text-gray-700 leading-relaxed dark:text-gray-300">
            { content || '正在分析数据，请稍候...' }
          </p>
        </div>
      </KeepAlive>

      <KeepAlive active={ type === 'markdown' && activeTab === 'markdown' }>
        <MdEditor
          ref={ mdEditorRef }
          key={ codeMode }
          content={ content }
          className="h-full border-none shadow-none"
          mdClassName="p-0"
          headerHeight={ mdHeaderHeight }
          title={ title }
          onChange={ (md) => {
            const target = reportStore.items.find(item => item.id === id)
            if (target) {
              target.content = md
            }
          } }
          renderHeader={ ({ toggleEditMode }) => (
            <div
              style={ {
                height: mdHeaderHeight,
              } }
              className="flex items-center justify-end px-4"
            >
              {/* { !curReportCodeMap?.mdIsProcessing && <div
                className="absolute right-30 cursor-pointer text-sm text-blue-500 transition-all duration-300 -top-9.4 hover:text-blue-600"
                onClick={ toggleEditMode }
              >
                Edit
              </div> } */}

              { metadata?.canTransformCode && <Switch
                disabled={ curReportCodeMap?.mdIsProcessing }
                checked={ activeTab === 'previewCode' }
                onChange={ checked => setActiveTab(checked
                  ? 'previewCode'
                  : 'markdown') }
                size="md"
                background="#e5e7eb"
                withGradient={ false }
                checkedIcon={ <BarChart3 size={ 12 } /> }
                uncheckedIcon={ <img src={ new URL('@/assets/svg/doc.svg', import.meta.url).href } /> }
                iconClassName={
                  activeTab === 'markdown'
                    ? 'bg-primary'
                    : 'bg-gradient-to-r from-blue-400 to-purple-500 dark:from-blue-500 dark:to-purple-600 text-white'
                }
              /> }
            </div>
          ) }
        />
      </KeepAlive>

      <KeepAlive active={ type === 'markdown' && activeTab === 'previewCode' }>
        <Loading loading={ !curReportCodeMap?.code }></Loading>

        <CodePreview
          mode={ codeMode }
          className={ cn(
            'h-full overflow-auto',
            isFullscreen && 'fixed inset-2 z-50',
          ) }
          readonly
          copyable={ false }
          code={ curReportCodeMap?.code || '' }
          language="html"
          title={ title || 'Chart' }
          headerHeight={ 48 }
          customHeader={ props => <div
            style={ {
              height: mdHeaderHeight,
            } }
            className="flex items-center justify-end gap-4 px-4"
          >
            <div
              className="cursor-pointer text-sm text-blue-500 transition-all duration-300 hover:text-blue-600"
              onClick={ () => {
                copyToClipboard(curReportCodeMap?.code || '').then(() => {
                  god.messageSuccess('Copied to clipboard')
                })
              } }
            >
              Copy
            </div>

            { metadata?.canTransformCode && <Switch
              checked={ activeTab === 'previewCode' }
              onChange={ checked => setActiveTab(checked
                ? 'previewCode'
                : 'markdown') }
              size="md"
              background="#e5e7eb"
              withGradient={ false }
              checkedIcon={ <BarChart3 size={ 12 } /> }
              uncheckedIcon={ <Code2 size={ 12 } /> }
              iconClassName="bg-gradient-to-r from-blue-400 to-purple-500 dark:from-blue-500 dark:to-purple-600 text-white"
            /> }
          </div> }
        />
      </KeepAlive>

      <KeepAlive active={ type === 'image' }>
        <div className="group relative">
          <img
            src={ content }
            alt={ title || '图片' }
            className="h-auto w-full rounded-lg shadow-sm transition-transform duration-300 group-hover:scale-[1.02]"
            loading="lazy"
          />
          { metadata?.description && (
            <div className="absolute inset-0 flex items-center justify-center rounded-lg bg-black/60 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
              <p className="px-4 text-center text-sm text-white">
                { metadata.description }
              </p>
            </div>
          ) }
        </div>
      </KeepAlive>

      <KeepAlive active={ type === 'video' }>
        <div className="group relative">
          <video
            src={ content }
            poster={ metadata?.thumbnail }
            controls
            className="h-auto w-full rounded-lg shadow-sm"
            preload="metadata"
          >
            您的浏览器不支持视频播放
          </video>
          { metadata?.duration && (
            <div className="absolute right-2 top-2 rounded bg-black/70 px-2 py-1 text-xs text-white">
              { formatDuration(metadata.duration) }
            </div>
          ) }
        </div>
      </KeepAlive>

      <KeepAlive active={ type === 'file' }>
        <div className="border border-gray-200 rounded-lg p-4 transition-colors duration-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <FileText className="h-8 w-8 text-blue-500 dark:text-blue-400" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="truncate text-sm text-gray-900 font-medium dark:text-gray-200">
                { title || '未命名文件' }
              </p>
              <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                { metadata?.mimeType && (
                  <span className="uppercase">
                    { metadata.mimeType.split('/')[1] }
                  </span>
                ) }
                { metadata?.size && (
                  <span>{ formatFileSize(metadata.size) }</span>
                ) }
              </div>
            </div>

            <Icon asChild>
              <a
                href={ content }
                download={ title }
                target="_blank"
                rel="noopener noreferrer"
              >
                <Download strokeWidth={ 1.5 } size={ 18 } />
              </a>
            </Icon>
          </div>
          { metadata?.description && (
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
              { metadata.description }
            </p>
          ) }
        </div>
      </KeepAlive>

      <KeepAlive active={ type === 'biji_data' }>
        { metadata?.bijiData && <BijiDataPreview data={ metadata.bijiData } /> }
      </KeepAlive>

      <KeepAlive active={ type === 'biji_list' }>
        { metadata?.bijiList && <BijiListPreview data={ metadata.bijiList } stateStore={ stateStore } /> }
      </KeepAlive>

      <KeepAlive active={ type === 'daren_list' }>
        { metadata?.darenList && <DaRenListPreview data={ metadata.darenList } /> }
      </KeepAlive>

      <KeepAlive active={ type === 'phone_preview' }>
        <div className="h-full overflow-auto">
          <PhonePreview
            content={ content }
            title={ title }
            imageUrl={ metadata?.phonePreview?.imageUrl }
            author={ metadata?.phonePreview?.author }
            authorAvatar={ metadata?.phonePreview?.authorAvatar }
            likes={ metadata?.phonePreview?.likes }
            shares={ metadata?.phonePreview?.shares }
            comments={ metadata?.phonePreview?.comments }
            scale={ metadata?.phonePreview?.scale || 0.8 }
          />
        </div>
      </KeepAlive>
    </motion.div>
  )
})

ReportContent.displayName = 'ReportContent'

export type ReportContentProps = {
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 自定义样式
   */
  style?: React.CSSProperties
  /**
   * 标题
   */
  item: ReportContentItem

  mdToCodePreview: MdToCodePreviewType
  reportStore: ReportStoreType
  stateStore: StateStoreType
}
