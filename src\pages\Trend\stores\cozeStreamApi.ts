/**
 * Coze 流式 API 调用模块
 *
 * 功能：
 * - 调用 Coze 工作流 API
 * - 解析 SSE (Server-Sent Events) 流式响应
 * - 处理流式数据并回调给调用方
 * - 支持完整的12个参数传递
 * - 提供参数验证和错误处理
 */

import type { StepNum } from '../types'
import insightReportData from '../assets/insight_report.json'
import { trendAg } from './index'

/**
 * Coze 流式接口响应数据结构
 */
export interface CozeStreamResponse {
  event: string
  data: string
  id?: string
}

/**
 * 解析的流式数据结构
 */
export interface ParsedStreamData {
  type: 'text' | 'title' | 'complete' | 'error'
  content: string
  title?: string
  error?: string
}

/**
 * Coze API 配置
 */
const COZE_CONFIG = {
  API_URL: '/coze-api/v1/workflow/stream_run', // 使用代理路径避免CORS问题
  /** 从环境变量获取Token，如果没有则使用Apifox配置中的Token作为fallback */
  API_TOKEN: import.meta.env.VITE_COZE_API_TOKEN || 'pat_34EmzbjjGaU1LET6Fx0pIshrzw2qLxaZPYPkr11bvKOUM9NnhQVMPMAhN24ofbxg',
  WORKFLOW_ID: '7535392121530286080',
}

/**
 * 请求参数数据 - 使用 insight_report.json 中的真实数据
 */
function getRequestData() {

  /** 验证必需参数 */
  const requiredParams = [
    'platform',
    'brand_name',
    'product_name',
    'industry_name',
    'competitor_name',
    'brand_data',
    'brand_report',
    'competitor_data',
    'daren_data',
    'industry_data',
    'industry_report',
    'product_data',
    'biji_data',
  ]

  const missingParams = requiredParams.filter(param =>
    !(param in insightReportData.parameters),
  )

  if (missingParams.length > 0) {
    console.error('[CozeAPI] 缺少必需参数:', missingParams)
  }

  const requestData = {
    workflow_id: insightReportData.workflow_id,
    parameters: {
      platform: insightReportData.parameters.platform || 'xhs',
      brand_name: insightReportData.parameters.brand_name || '卡姿兰',
      product_name: insightReportData.parameters.product_name || '黑磁散粉',
      industry_name: insightReportData.parameters.industry_name || '美妆护肤',
      competitor_name: insightReportData.parameters.competitor_name || '完美日记',
      brand_data: insightReportData.parameters.brand_data || '',
      brand_report: insightReportData.parameters.brand_report || '',
      competitor_data: insightReportData.parameters.competitor_data || '',
      daren_data: insightReportData.parameters.daren_data || '',
      industry_data: insightReportData.parameters.industry_data || '',
      industry_report: insightReportData.parameters.industry_report || '',
      product_data: insightReportData.parameters.product_data || '',
      biji_data: insightReportData.parameters.biji_data,
    },
  }

  return requestData
}

/**
 * SSE事件解析器
 */
class SSEParser {
  private currentEvent: string = ''
  private currentData: string = ''
  private currentId: string = ''

  /**
   * 解析SSE数据行
   */
  parseLine(line: string): ParsedStreamData | null {
    const trimmedLine = line.trim()

    if (!trimmedLine) {
      /** 空行表示事件结束，处理当前事件 */
      return this.processEvent()
    }

    if (trimmedLine.startsWith('event: ')) {
      this.currentEvent = trimmedLine.substring(7).trim()
    }
    else if (trimmedLine.startsWith('data: ')) {
      this.currentData = trimmedLine.substring(6).trim()
    }
    else if (trimmedLine.startsWith('id: ')) {
      this.currentId = trimmedLine.substring(4).trim()
    }

    return null
  }

  /**
   * 处理完整的SSE事件
   */
  private processEvent(): ParsedStreamData | null {
    try {
      if (!this.currentData) {
        this.reset()
        return null
      }

      /** 处理done事件 */
      if (this.currentEvent === 'done') {
        this.reset()
        return {
          type: 'complete',
          content: '',
        }
      }

      /** 处理message事件 */
      if (this.currentEvent === 'message') {
        const data = JSON.parse(this.currentData)

        if (data.content) {
          const nodeTitle = data.node_title || ''
          const contentType = data.content_type
          
          /** 根据 node_title 判断内容类型 */
          /** 1. 标题类型：node_title 包含 "name" 或 "title" */
          if (nodeTitle.includes('name') || nodeTitle.includes('title')) {
            this.reset()
            return {
              type: 'title',
              content: data.content,
              title: data.content,
            }
          }
          
          /** 2. 正文内容：node_title 为 "insight_report", "insight_report1", "thinking" 等 */
          /** 3. content_type 为 "0" 或 "text" 都作为文本处理 */
          if (nodeTitle === 'insight_report' || 
              nodeTitle === 'insight_report1' || 
              nodeTitle === 'thinking' ||
              contentType === '0' || 
              contentType === 'text') {
            this.reset()
            return {
              type: 'text',
              content: data.content,
            }
          }

          /** 默认作为文本内容处理 */
          this.reset()
          return {
            type: 'text',
            content: data.content,
          }
        }
      }

      this.reset()
      return null
    }
    catch (error) {
      console.error('[CozeAPI] ❌ 解析SSE事件失败:', error)
      this.reset()
      return {
        type: 'error',
        content: '',
        error: `解析失败: ${error}`,
      }
    }
  }

  /**
   * 重置解析器状态
   */
  private reset() {
    this.currentEvent = ''
    this.currentData = ''
    this.currentId = ''
  }
}

/**
 * 调用 Coze 流式接口
 */
export async function callCozeStreamAPI(
  onData: (data: ParsedStreamData) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
): Promise<void> {
  try {
    console.log('[CozeAPI] 开始调用流式接口')

    const requestData = getRequestData()

    const response = await fetch(COZE_CONFIG.API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${COZE_CONFIG.API_TOKEN}`,
        'Accept': 'text/event-stream',
      },
      body: JSON.stringify(requestData),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('无法获取响应流')
    }

    const decoder = new TextDecoder()
    const sseParser = new SSEParser()
    let buffer = ''

    while (true) {
      const { done, value } = await reader.read()

      if (done) {
        /** 处理最后的缓冲区数据 */
        if (buffer.trim()) {
          const parsedData = sseParser.parseLine('')
          if (parsedData) {
            onData(parsedData)
          }
        }
        console.log('[CozeAPI] ✅ 流式响应完成')
        onComplete?.()
        break
      }

      /** 解码数据并添加到缓冲区 */
      buffer += decoder.decode(value, { stream: true })

      /** 按行分割数据 */
      const lines = buffer.split('\n')
      buffer = lines.pop() || '' // 保留最后一个不完整的行

      /** 处理每一行 */
      for (const line of lines) {
        const parsedData = sseParser.parseLine(line)
        if (parsedData) {
          onData(parsedData)
        }
      }
    }
  }
  catch (error) {
    console.error('[CozeAPI] ❌ 接口调用失败:', error)
    onError?.(error as Error)
    throw error
  }
}

/**
 * 模拟流式数据（用于测试）
 */
export async function mockCozeStreamAPI(
  onData: (data: ParsedStreamData) => void,
  _onError?: (error: Error) => void,
  onComplete?: () => void,
): Promise<void> {
  console.log('[CozeAPI] 使用模拟数据进行测试')

  const mockData = [
    { type: 'title' as const, content: '小红书营销趋势分析报告', title: '小红书营销趋势分析报告' },
    { type: 'text' as const, content: '## 1. 数据概览\n\n本次分析基于小红书平台的热门内容数据，涵盖了美妆、生活方式、时尚等多个领域。' },
    { type: 'text' as const, content: '\n\n## 2. 热门趋势\n\n### 美妆类内容\n- 韩系美甲成为热门话题\n- 护肤产品测评受到关注\n- 化妆教程互动性强' },
    { type: 'text' as const, content: '\n\n### 生活方式\n- 旅行分享内容增长明显\n- 日常记录类内容受欢迎\n- 情感类内容互动率高' },
    { type: 'text' as const, content: '\n\n## 3. 营销建议\n\n1. **内容策略**：关注美妆和生活方式类内容\n2. **发布时间**：晚上8-10点互动率最高\n3. **话题标签**：使用热门标签提升曝光' },
    { type: 'complete' as const, content: '' },
  ]

  for (let i = 0; i < mockData.length; i++) {
    await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 400))
    onData(mockData[i])
  }

  onComplete?.()
}
